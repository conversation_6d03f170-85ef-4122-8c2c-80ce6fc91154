{"name": "gtoken", "version": "8.0.0", "description": "Node.js Google Authentication Service Account Tokens", "main": "./build/cjs/src/index.cjs", "type": "module", "types": "./build/cjs/src/index.d.ts", "engines": {"node": ">=18"}, "repository": "google/node-gtoken", "scripts": {"lint": "gts check", "clean": "rm -rf build", "fix": "gts fix", "compile:esm": "tsc -p ./tsconfig.esm.json", "compile:cjs": "tsc -p ./tsconfig.json && npm run babel", "compile": "npm run compile:esm && npm run compile:cjs", "prepare": "npm run compile", "pretest": "npm run compile", "presystem-test": "npm run compile", "system-test:cjs": "mocha build/cjs/system-test --timeout 600000", "system-test:esm": "mocha build/esm/system-test --timeout 600000 --experimental-modules", "system-test": "npm run system-test:esm && npm run system-test:cjs", "test:cjs": "c8 mocha --timeout=5000 build/cjs/test", "test:esm": "c8 mocha build/esm/test --loader=esmock", "test": "npm run test:esm && npm run test:cjs", "docs": "jsdoc -c .jsdoc.json", "predocs-test": "npm run docs", "samples-test": "cd samples/ && npm link ../ && npm test && cd ../", "prelint": "cd samples; npm link ../; npm install", "precompile": "rm -rf build", "babel": "babel esm --out-dir build/cjs --ignore \"esm/**/*.d.ts\" --extensions \".ts\" --out-file-extension .cjs --copy-files"}, "exports": {".": {"import": {"types": "./build/esm/src/index.d.ts", "default": "./build/esm/src/index.js"}, "require": {"types": "./build/cjs/src/index.d.ts", "default": "./build/cjs/src/index.cjs"}}}, "keywords": ["google", "service", "account", "api", "token", "api", "auth"], "author": {"name": "Google, LLC"}, "license": "MIT", "dependencies": {"gaxios": "^7.0.0", "jws": "^4.0.0"}, "devDependencies": {"@babel/cli": "^7.23.0", "@babel/core": "^7.23.0", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/preset-env": "^7.22.20", "@babel/preset-typescript": "^7.23.0", "@types/jws": "^3.2.10", "@types/mocha": "^10.0.9", "@types/node": "^22.9.0", "babel-plugin-replace-import-extension": "^1.1.4", "c8": "^10.1.2", "esmock": "^2.6.9", "gapic-tools": "^0.4.6", "gts": "^6.0.2", "jsdoc": "^4.0.4", "jsdoc-fresh": "^4.0.0", "jsdoc-region-tag": "^3.0.0", "linkinator": "^6.1.2", "mocha": "^10.8.2", "nock": "^14.0.0", "pack-n-play": "^2.1.0", "pdfmake": "^0.2.15", "proxyquire": "^2.1.3", "typescript": "^5.6.3"}, "files": ["build/esm", "build/cjs", "!build/cjs/system-test", "!build/esm/system-test", "!build/cjs/test", "!build/esm/test", "!build/esm/**/*.map", "!build/cjs/**/*.map"]}