# Script PowerShell para iniciar servidor GA4 API + ngrok
Write-Host "🚀 Iniciando servidor GA4 API e túnel ngrok..." -ForegroundColor Green
Write-Host ""

# Caminho do ngrok
$ngrokPath = "C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Ngrok.Ngrok_Microsoft.Winget.Source_8wekyb3d8bbwe\ngrok.exe"

# Verificar se o ngrok existe
if (-not (Test-Path $ngrokPath)) {
    Write-Host "❌ Erro: ngrok não encontrado no caminho esperado" -ForegroundColor Red
    Write-Host "Instale o ngrok com: winget install ngrok" -ForegroundColor Yellow
    exit 1
}

# Função para verificar se a porta está em uso
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    }
    catch {
        return $false
    }
}

# Verificar se a porta 3000 está livre
if (Test-Port -Port 3000) {
    Write-Host "⚠️  Porta 3000 já está em uso. Parando processos existentes..." -ForegroundColor Yellow
    # Tentar parar processos na porta 3000
    $processes = Get-NetTCPConnection -LocalPort 3000 -ErrorAction SilentlyContinue
    if ($processes) {
        $processes | ForEach-Object {
            Stop-Process -Id $_.OwningProcess -Force -ErrorAction SilentlyContinue
        }
    }
    Start-Sleep -Seconds 2
}

Write-Host "📊 Iniciando servidor Node.js..." -ForegroundColor Cyan

# Iniciar servidor em background
$serverJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    npm start
}

# Aguardar servidor inicializar
Write-Host "⏳ Aguardando servidor inicializar..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# Verificar se o servidor está rodando
if (Test-Port -Port 3000) {
    Write-Host "✅ Servidor iniciado com sucesso na porta 3000" -ForegroundColor Green
} else {
    Write-Host "❌ Erro: Servidor não conseguiu iniciar na porta 3000" -ForegroundColor Red
    Stop-Job $serverJob -ErrorAction SilentlyContinue
    Remove-Job $serverJob -ErrorAction SilentlyContinue
    exit 1
}

Write-Host ""
Write-Host "🌐 Iniciando túnel ngrok..." -ForegroundColor Cyan
Write-Host ""
Write-Host "⚠️  IMPORTANTE: Após iniciar o ngrok, você verá uma URL como:" -ForegroundColor Yellow
Write-Host "   https://abc123.ngrok.io" -ForegroundColor White
Write-Host ""
Write-Host "📋 Seus endpoints públicos serão:" -ForegroundColor Cyan
Write-Host "   https://abc123.ngrok.io/health" -ForegroundColor White
Write-Host "   https://abc123.ngrok.io/ga4" -ForegroundColor White
Write-Host "   https://abc123.ngrok.io/ga4/llm" -ForegroundColor White
Write-Host "   https://abc123.ngrok.io/ga4/metrics" -ForegroundColor White
Write-Host "   https://abc123.ngrok.io/ga4/dimensions" -ForegroundColor White
Write-Host ""
Write-Host "🔗 Interface web do ngrok: http://localhost:4040" -ForegroundColor Magenta
Write-Host ""
Write-Host "🛑 Para parar tudo, pressione Ctrl+C" -ForegroundColor Red
Write-Host ""

# Registrar handler para limpeza ao sair
Register-EngineEvent -SourceIdentifier PowerShell.Exiting -Action {
    Write-Host "🧹 Limpando processos..." -ForegroundColor Yellow
    Stop-Job $serverJob -ErrorAction SilentlyContinue
    Remove-Job $serverJob -ErrorAction SilentlyContinue
}

# Iniciar ngrok (processo principal)
try {
    & $ngrokPath http 3000
}
finally {
    Write-Host ""
    Write-Host "🧹 Parando servidor..." -ForegroundColor Yellow
    Stop-Job $serverJob -ErrorAction SilentlyContinue
    Remove-Job $serverJob -ErrorAction SilentlyContinue
    Write-Host "✅ Processos finalizados" -ForegroundColor Green
}
