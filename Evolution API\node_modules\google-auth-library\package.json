{"name": "google-auth-library", "version": "10.1.0", "author": "Google Inc.", "description": "Google APIs Authentication Client Library for Node.js", "engines": {"node": ">=18"}, "main": "./build/src/index.js", "types": "./build/src/index.d.ts", "repository": "googleapis/google-auth-library-nodejs.git", "keywords": ["google", "api", "google apis", "client", "client library"], "dependencies": {"base64-js": "^1.3.0", "ecdsa-sig-formatter": "^1.0.11", "gaxios": "^7.0.0", "gcp-metadata": "^7.0.0", "google-logging-utils": "^1.0.0", "gtoken": "^8.0.0", "jws": "^4.0.0"}, "devDependencies": {"@types/base64-js": "^1.2.5", "@types/jws": "^3.1.0", "@types/mocha": "^10.0.10", "@types/mv": "^2.1.0", "@types/ncp": "^2.0.1", "@types/node": "^22.0.0", "@types/sinon": "^17.0.0", "assert-rejects": "^1.0.0", "c8": "^10.0.0", "codecov": "^3.0.2", "gts": "^6.0.0", "is-docker": "^3.0.0", "jsdoc": "^4.0.0", "jsdoc-fresh": "^4.0.0", "jsdoc-region-tag": "^3.0.0", "karma": "^6.0.0", "karma-chrome-launcher": "^3.0.0", "karma-coverage": "^2.0.0", "karma-firefox-launcher": "^2.0.0", "karma-mocha": "^2.0.0", "karma-sourcemap-loader": "^0.4.0", "karma-webpack": "^5.0.1", "keypair": "^1.0.4", "linkinator": "^6.1.2", "mocha": "^11.1.0", "mv": "^2.1.1", "ncp": "^2.0.0", "nock": "^14.0.1", "null-loader": "^4.0.0", "puppeteer": "^24.0.0", "sinon": "^18.0.1", "ts-loader": "^8.0.0", "typescript": "^5.1.6", "webpack": "^5.21.2", "webpack-cli": "^4.0.0"}, "files": ["build/src", "!build/src/**/*.map"], "scripts": {"test": "c8 mocha build/test", "clean": "gts clean", "prepare": "npm run compile", "lint": "gts check --no-inline-config", "compile": "tsc -p .", "fix": "gts fix", "pretest": "npm run compile -- --sourceMap", "docs": "jsdoc -c .jsdoc.js", "samples-setup": "cd samples/ && npm link ../ && npm run setup && cd ../", "samples-test": "cd samples/ && npm link ../ && npm test && cd ../", "system-test": "mocha build/system-test --timeout 60000", "presystem-test": "npm run compile -- --sourceMap", "webpack": "webpack", "browser-test": "karma start", "docs-test": "linkinator docs", "predocs-test": "npm run docs", "prelint": "cd samples; npm link ../; npm install"}, "license": "Apache-2.0"}