# 🎯 Guia Visual: Como Configurar o ngrok

## 📋 Resumo Rápido
1. ✅ **ngrok já está instalado** no seu computador
2. ⚠️ **Falta apenas:** Configurar o token de autenticação
3. 🚀 **Depois:** Usar os scripts prontos

---

## 🔑 PASSO 1: Configurar <PERSON> (OBRIGATÓRIO)

### Opção A: Script Automático (MAIS FÁCIL) 🌟
```bash
# Clique duplo no arquivo ou execute no terminal:
.\configurar-ngrok.bat
```

**O que acontece:**
1. Script mostra instruções
2. Abre página do ngrok automaticamente
3. Você cola o token
4. Script configura tudo automaticamente

### Opção B: Manual
1. **Abrir navegador:** https://dashboard.ngrok.com/signup
2. **Criar conta gratuita** (se não tiver)
3. **Fazer login**
4. **Ir para:** https://dashboard.ngrok.com/get-started/your-authtoken
5. **Copiar o token** (algo como: `2abc123def456ghi789jkl`)
6. **Abrir terminal** (PowerShell ou CMD)
7. **Executar comando:**
   ```bash
   C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Ngrok.Ngrok_Microsoft.Winget.Source_8wekyb3d8bbwe\ngrok.exe authtoken SEU_TOKEN_AQUI
   ```
   *(Substitua `SEU_TOKEN_AQUI` pelo token copiado)*

---

## 🚀 PASSO 2: Usar o Túnel

### Opção A: Script Completo (RECOMENDADO) 🌟
```bash
# Clique duplo no arquivo ou execute:
.\start-tunnel.bat
```

**O que acontece:**
1. ✅ Inicia servidor GA4 na porta 3000
2. ✅ Cria túnel público com ngrok
3. ✅ Mostra URL pública (ex: `https://abc123.ngrok.io`)

### Opção B: Comandos Separados
```bash
# Terminal 1: Iniciar servidor
npm start

# Terminal 2: Iniciar túnel
npm run tunnel
```

---

## 📱 Exemplo Visual do Resultado

### Antes (Local):
```
❌ http://localhost:3000/health
   (Só funciona no seu computador)
```

### Depois (Público):
```
✅ https://abc123.ngrok.io/health
   (Funciona em qualquer lugar do mundo)
```

---

## 🧪 Como Testar

### 1. Executar o túnel:
```bash
.\start-tunnel.bat
```

### 2. Você verá algo assim:
```
🚀 Servidor GA4 API rodando em http://localhost:3000
🌐 Iniciando túnel ngrok...

ngrok by @inconshreveable

Session Status    online
Account           <EMAIL>
Forwarding        https://abc123.ngrok.io -> http://localhost:3000
```

### 3. Testar no navegador:
- Abra: `https://abc123.ngrok.io/health`
- Deve mostrar: `{"status":"OK",...}`

---

## 🤖 Para LLMs

### Seus endpoints públicos:
```
https://abc123.ngrok.io/health          ← Status
https://abc123.ngrok.io/ga4             ← Consulta GA4
https://abc123.ngrok.io/ga4/llm         ← Dados para LLM
https://abc123.ngrok.io/ga4/metrics     ← Lista métricas
https://abc123.ngrok.io/ga4/dimensions  ← Lista dimensões
```

### Exemplo de uso:
```javascript
// Substitua pela sua URL do ngrok
const API_URL = 'https://abc123.ngrok.io';

const response = await fetch(`${API_URL}/ga4/llm`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    propertyId: 'properties/*********',
    startDate: '2024-06-01',
    endDate: '2024-06-28',
    query: 'Analise o tráfego do site'
  })
});
```

---

## ❓ Problemas Comuns

### "authentication failed"
**Solução:** Execute `.\configurar-ngrok.bat` para configurar o token

### "Port already in use"
**Solução:** Feche outros programas na porta 3000 ou mude a porta

### "ngrok not found"
**Solução:** Os scripts já usam o caminho completo, deve funcionar

### Token inválido
**Solução:** Verifique se copiou o token completo do dashboard

---

## 🎯 Checklist Final

- [ ] ✅ ngrok instalado (já feito)
- [ ] 🔑 Token configurado (`.\configurar-ngrok.bat`)
- [ ] 🚀 Túnel funcionando (`.\start-tunnel.bat`)
- [ ] 🧪 Teste realizado (acessar URL pública)
- [ ] 🤖 Integração com LLM (usar URL pública)

---

**🎉 Pronto! Seu servidor GA4 está acessível globalmente!**
