# 🔧 Configuração do Servidor GA4 API

## ✅ Status Atual
- ✅ Servidor configurado e funcionando
- ✅ Dependências instaladas
- ✅ Endpoints básicos testados
- ⚠️ Necessário configurar credenciais reais do GA4

## 🚀 Próximos Passos

### 1. Configurar Property ID do GA4
No arquivo `server.js`, substitua `'properties/SEU_PROPERTY_ID'` pelo seu Property ID real:

```javascript
// Linha 17 e outras ocorrências
const propertyId = req.body.propertyId || 'properties/*********'; // Seu ID aqui
```

**Como encontrar seu Property ID:**
1. Acesse Google Analytics 4
2. Vá em Admin > Configurações da propriedade
3. Copie o ID numérico (ex: *********)
4. Use no formato: `properties/*********`

### 2. Verificar Credenciais do Google Cloud
Certifique-se de que o arquivo `projeto-ga4-e-ia-b2180899d811.json` tem as permissões corretas:

- ✅ Google Analytics Reporting API habilitada
- ✅ Conta de serviço com acesso ao GA4
- ✅ Arquivo JSON na raiz do projeto

### 3. Testar com Dados Reais
Após configurar o Property ID, execute:

```bash
npm start
npm test
```

## 🤖 Integração com LLM

### Endpoints Otimizados para IA:

**1. `/ga4/llm` - Dados formatados para LLM**
```json
{
  "propertyId": "properties/*********",
  "startDate": "2024-06-01", 
  "endDate": "2024-06-28",
  "query": "Analise o desempenho do site"
}
```

**2. `/ga4` - Consulta flexível**
```json
{
  "propertyId": "properties/*********",
  "metrics": ["sessions", "users", "pageviews"],
  "dimensions": ["country", "deviceCategory"],
  "limit": 10
}
```

### Exemplo de Uso com LLM:
```javascript
// Buscar dados do GA4
const response = await fetch('http://localhost:3000/ga4/llm', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    propertyId: 'properties/*********',
    startDate: '2024-06-01',
    endDate: '2024-06-28',
    query: 'Analise tendências de tráfego'
  })
});

const data = await response.json();

// Enviar para LLM
const llmPrompt = `
Analise os seguintes dados do Google Analytics:
${data.summary}

Dados detalhados: ${JSON.stringify(data.rawData)}

${data.query}
`;
```

## 📊 Métricas e Dimensões Disponíveis

### Métricas Principais:
- `sessions` - Sessões
- `users` - Usuários
- `pageviews` - Visualizações de página
- `bounceRate` - Taxa de rejeição
- `conversions` - Conversões
- `totalRevenue` - Receita total

### Dimensões Principais:
- `country` - País
- `deviceCategory` - Categoria do dispositivo
- `source` - Fonte de tráfego
- `medium` - Meio
- `pagePath` - Caminho da página
- `eventName` - Nome do evento

## 🔒 Segurança

### Variáveis de Ambiente (Recomendado):
Crie um arquivo `.env`:
```
PORT=3000
GA4_PROPERTY_ID=properties/*********
GA4_CREDENTIALS_PATH=./projeto-ga4-e-ia-b2180899d811.json
```

### Instalar dotenv:
```bash
npm install dotenv
```

### Atualizar server.js:
```javascript
require('dotenv').config();
const propertyId = process.env.GA4_PROPERTY_ID || 'properties/SEU_PROPERTY_ID';
```

## 🚀 Comandos Úteis

```bash
# Iniciar servidor
npm start

# Modo desenvolvimento
npm run dev

# Executar testes
npm test

# Testar health check
npm run test:health

# Testar métricas
npm run test:metrics
```

## 📝 Logs e Debug

Para logs detalhados, execute em modo desenvolvimento:
```bash
NODE_ENV=development npm start
```

## ✅ Checklist Final

- [ ] Property ID configurado
- [ ] Credenciais do Google Cloud válidas
- [ ] Testes passando com dados reais
- [ ] Integração com LLM funcionando
- [ ] Variáveis de ambiente configuradas (opcional)

Seu servidor está pronto para integração com Agentes LLM! 🎉
