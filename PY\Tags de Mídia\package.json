{"name": "ga4-llm-endpoint", "version": "1.0.0", "description": "Endpoint local para puxar dados do GA4 via API e trabalhar com Agente LLM", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "@google-analytics/data": "^4.0.0", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["ga4", "google-analytics", "api", "llm", "express"], "author": "", "license": "MIT"}