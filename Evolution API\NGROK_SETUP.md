# 🌐 Configuração do ngrok para GA4 API

## 🚀 O que é o ngrok?
O ngrok cria um túnel seguro que expõe seu servidor local (localhost:3000) para a internet, permitindo que APIs externas e LLMs acessem seu endpoint GA4.

## ⚡ Comandos Rápidos

### 1. <PERSON><PERSON><PERSON><PERSON> (Recomendado)
```bash
# Windows Batch
.\start-tunnel.bat

# PowerShell (mais a<PERSON>)
.\start-tunnel.ps1
```

### 2. Comandos NPM
```bash
# Apenas túnel ngrok
npm run tunnel

# Servidor + ngrok simultaneamente
npm run start:tunnel
```

### 3. Manual (duas janelas de terminal)
```bash
# Terminal 1: Servidor
npm start

# Terminal 2: Túnel ngrok
npm run tunnel
```

## 🔧 Configuração Inicial (OBRIGATÓRIO)

### 1. Criar conta no ngrok (Gratuito)
1. Acesse: https://dashboard.ngrok.com/signup
2. Crie uma conta gratuita
3. Acesse: https://dashboard.ngrok.com/get-started/your-authtoken
4. Copie seu authtoken

### 2. Configurar authtoken (OBRIGATÓRIO)
```bash
C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Ngrok.Ngrok_Microsoft.Winget.Source_8wekyb3d8bbwe\ngrok.exe authtoken SEU_TOKEN_AQUI
```

**⚠️ IMPORTANTE:** O ngrok não funcionará sem o authtoken configurado!

### 3. Verificar configuração
```bash
C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Ngrok.Ngrok_Microsoft.Winget.Source_8wekyb3d8bbwe\ngrok.exe config check
```

## 🌍 Como Usar

### Cenário 1: Desenvolvimento Local
```bash
# Terminal 1: Iniciar servidor
npm start

# Terminal 2: Iniciar túnel
npm run tunnel
```

### Cenário 2: Tudo junto (Recomendado)
```bash
npm run start:tunnel
```

Você verá algo como:
```
🚀 Servidor GA4 API rodando em http://localhost:3000
ngrok by @inconshreveable

Session Status    online
Account           <EMAIL>
Version           3.x.x
Region            United States (us)
Forwarding        https://abc123.ngrok.io -> http://localhost:3000
```

## 🔗 URLs Públicas

Após iniciar o ngrok, seus endpoints estarão disponíveis publicamente:

### Endpoints Locais → Públicos
- `http://localhost:3000/health` → `https://abc123.ngrok.io/health`
- `http://localhost:3000/ga4` → `https://abc123.ngrok.io/ga4`
- `http://localhost:3000/ga4/llm` → `https://abc123.ngrok.io/ga4/llm`

## 🤖 Integração com LLM

### Exemplo com OpenAI/ChatGPT:
```javascript
// Usar a URL pública do ngrok
const GA4_API_URL = 'https://abc123.ngrok.io';

async function getGA4Data() {
  const response = await fetch(`${GA4_API_URL}/ga4/llm`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      propertyId: 'properties/*********',
      startDate: '2024-06-01',
      endDate: '2024-06-28',
      query: 'Analise o desempenho do site'
    })
  });
  
  return await response.json();
}
```

### Exemplo com Webhook:
```javascript
// Configurar webhook para receber dados
app.post('/webhook', (req, res) => {
  const { message } = req.body;
  
  // Processar solicitação do LLM
  if (message.includes('analytics')) {
    // Buscar dados GA4 e responder
    getGA4Data().then(data => {
      // Enviar resposta para o LLM
      res.json({ response: data.summary });
    });
  }
});
```

## 🔒 Segurança

### Limitações da Conta Gratuita:
- ✅ HTTPS automático
- ✅ Subdomínio aleatório
- ⚠️ URL muda a cada reinicialização
- ⚠️ Limite de conexões simultâneas

### Conta Paga (Recomendado para produção):
- ✅ Subdomínio personalizado
- ✅ URL fixa
- ✅ Mais conexões simultâneas
- ✅ Autenticação básica

### Configurar Autenticação Básica:
```bash
ngrok http 3000 --basic-auth "usuario:senha"
```

## 📊 Monitoramento

### Interface Web do ngrok:
Acesse `http://localhost:4040` para ver:
- Requisições em tempo real
- Logs detalhados
- Estatísticas de uso
- Replay de requisições

## 🛠️ Troubleshooting

### Problema: "command not found: ngrok"
```bash
# Reinstalar globalmente
npm install -g ngrok
```

### Problema: "tunnel session failed"
```bash
# Configurar authtoken
ngrok authtoken SEU_TOKEN
```

### Problema: Porta já em uso
```bash
# Usar porta diferente
ngrok http 3001
# E atualizar PORT no .env para 3001
```

## 📝 Exemplo Completo

### 1. Iniciar tudo:
```bash
npm run start:tunnel
```

### 2. Testar endpoint público:
```bash
curl https://abc123.ngrok.io/health
```

### 3. Testar com dados GA4:
```bash
curl -X POST https://abc123.ngrok.io/ga4/llm \
  -H "Content-Type: application/json" \
  -d '{
    "propertyId": "properties/*********",
    "startDate": "2024-06-01",
    "endDate": "2024-06-28",
    "query": "Analise o tráfego do site"
  }'
```

Agora seu servidor GA4 está acessível globalmente! 🌍
