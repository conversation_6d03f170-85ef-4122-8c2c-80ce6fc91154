# GA4 API Server para LLM

Este servidor Express.js fornece uma API local para consultar dados do Google Analytics 4 e formatá-los para uso com Agentes LLM.

## 🚀 Configuração Inicial

### 1. Instalar Dependências
```bash
npm install
```

### 2. Configurar Credenciais do Google
- Certifique-se de que o arquivo `projeto-ga4-e-ia-b2180899d811.json` está na raiz do projeto
- Este arquivo contém as credenciais de serviço do Google Cloud

### 3. Atualizar Property ID
No arquivo `server.js`, substitua `'properties/SEU_PROPERTY_ID'` pelo seu Property ID real do GA4.

## 📊 Endpoints Disponíveis

### POST /ga4
Consulta dados do GA4 com parâmetros personalizáveis.

**Parâmetros do Body:**
```json
{
  "propertyId": "properties/123456789",
  "startDate": "2024-06-01",
  "endDate": "2024-06-28",
  "metrics": ["sessions", "users", "pageviews"],
  "dimensions": ["country", "deviceCategory"],
  "limit": 10
}
```

**Resposta:**
```json
{
  "summary": {
    "totalRows": 50,
    "dateRange": { "startDate": "2024-06-01", "endDate": "2024-06-28" },
    "metrics": ["sessions", "users"],
    "dimensions": ["country"]
  },
  "data": [
    {
      "country": "Brazil",
      "sessions": "1234",
      "users": "987"
    }
  ],
  "llmSummary": "Dados do Google Analytics 4 para o período..."
}
```

### POST /ga4/llm
Endpoint otimizado para consumo por LLM com dados pré-formatados.

**Parâmetros do Body:**
```json
{
  "propertyId": "properties/123456789",
  "startDate": "2024-06-01",
  "endDate": "2024-06-28",
  "query": "Analise o desempenho do site"
}
```

### GET /ga4/metrics
Lista métricas disponíveis do GA4.

### GET /ga4/dimensions
Lista dimensões disponíveis do GA4.

### GET /health
Verifica se o servidor está funcionando.

## 🔧 Como Usar

### 1. Iniciar o Servidor
```bash
npm start
```

O servidor estará disponível em `http://localhost:3000`

### 2. Exemplo de Requisição
```bash
curl -X POST http://localhost:3000/ga4 \
  -H "Content-Type: application/json" \
  -d '{
    "propertyId": "properties/SEU_PROPERTY_ID",
    "startDate": "2024-06-01",
    "endDate": "2024-06-28",
    "metrics": ["sessions", "users"],
    "dimensions": ["country"]
  }'
```

## 🤖 Integração com LLM

O endpoint `/ga4/llm` foi especialmente projetado para fornecer dados formatados para Agentes LLM:

- Resumo textual dos dados
- Dados estruturados limitados para não sobrecarregar o contexto
- Sugestões de análises
- Formato otimizado para processamento por IA

## 📝 Notas Importantes

- Certifique-se de que as credenciais do Google Cloud têm permissão para acessar a API do GA4
- O Property ID deve estar no formato `properties/123456789`
- As datas devem estar no formato `YYYY-MM-DD`
- O servidor inclui CORS habilitado para requisições de diferentes origens

## 🛠️ Desenvolvimento

Para executar em modo de desenvolvimento com logs detalhados:
```bash
NODE_ENV=development npm start
```
