{"name": "retry-request", "version": "8.0.0", "description": "Retry a request.", "main": "index.js", "repository": "stephenplusplus/retry-request", "scripts": {"docs": "jsdoc -c .jsdoc.js", "predocs-test": "npm run docs", "docs-test": "linkinator docs", "fix": "gts fix", "lint": "gts check", "test": "mocha --timeout 30000", "system-test": ""}, "files": ["index.js", "index.d.ts", "license"], "types": "index.d.ts", "keywords": ["request", "retry", "stream"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "engines": {"node": ">=18"}, "dependencies": {"@types/request": "^2.48.12", "extend": "^3.0.2", "teeny-request": "^10.0.0"}, "devDependencies": {"async": "^3.2.6", "gts": "^6.0.2", "jsdoc": "^4.0.4", "jsdoc-fresh": "^3.0.0", "jsdoc-region-tag": "^3.0.0", "linkinator": "^6.1.2", "lodash.range": "^3.2.0", "mocha": "^11.1.0", "typescript": "^5.7.3"}}