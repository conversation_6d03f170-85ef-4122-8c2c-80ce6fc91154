{"name": "proto3-json-serializer", "version": "3.0.0", "repository": "googleapis/proto3-json-serializer-nodejs", "description": "Support for proto3 JSON serialiazation/deserialization for protobuf.js", "main": "build/src/index.js", "types": "build/src/index.d.ts", "files": ["build/src"], "license": "Apache-2.0", "keywords": ["protobufjs", "protobuf.js", "protobuf", "proto3", "json", "serialization", "deserialization"], "scripts": {"test": "c8 mocha build/test/unit", "system-test": "mocha build/test/system", "lint": "gts lint", "clean": "gts clean", "compile": "tsc", "fix": "gts fix", "prepare": "npm run compile", "pretest": "npm run compile", "posttest": "npm run lint", "compile-test-protos": "cd test-fixtures/proto && pbjs -t json test.proto > test.json", "docs": "jsdoc -c .jsdoc.js", "docs-test": "linkinator docs", "predocs-test": "npm run docs", "samples-test": "cd samples/ && npm link ../ && npm test && cd ../", "prelint": "cd samples && npm link ../ && npm install"}, "dependencies": {"protobufjs": "^7.4.0"}, "devDependencies": {"@types/mocha": "^10.0.10", "@types/node": "^22.13.1", "c8": "^10.1.3", "google-proto-files": "^4.2.0", "gts": "^6.0.2", "jsdoc": "^4.0.4", "jsdoc-fresh": "^3.0.0", "jsdoc-region-tag": "^3.0.0", "linkinator": "^6.1.2", "mocha": "^11.1.0", "pack-n-play": "^2.1.0", "protobufjs-cli": "^1.1.3", "typescript": "^5.7.3"}, "engines": {"node": ">=18"}}