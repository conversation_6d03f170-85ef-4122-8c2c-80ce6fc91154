# Changelog

## [10.1.0](https://github.com/googleapis/teeny-request/compare/v10.0.0...v10.1.0) (2025-04-07)


### Features

* Support fetch Headers class ([#359](https://github.com/googleapis/teeny-request/issues/359)) ([e9b1577](https://github.com/googleapis/teeny-request/commit/e9b1577f921a48f7947d4df26ab93747bf6b61d8))

## [10.0.0](https://github.com/googleapis/teeny-request/compare/v9.0.0...v10.0.0) (2025-02-15)


### ⚠ BREAKING CHANGES

* upgrade to Node 18 ([#353](https://github.com/googleapis/teeny-request/issues/353))

### Miscellaneous Chores

* Upgrade to Node 18 ([#353](https://github.com/googleapis/teeny-request/issues/353)) ([4a5c834](https://github.com/googleapis/teeny-request/commit/4a5c834451a5649f68301385356677d8b3809054))

## [9.0.0](https://github.com/googleapis/teeny-request/compare/v8.0.3...v9.0.0) (2023-07-20)


### ⚠ BREAKING CHANGES

* move minimum version to node 14 ([#312](https://github.com/googleapis/teeny-request/issues/312))

### Miscellaneous Chores

* Move minimum version to node 14 ([#312](https://github.com/googleapis/teeny-request/issues/312)) ([e30cf76](https://github.com/googleapis/teeny-request/commit/e30cf765fb7997e36cf7cc8144dfe8480d12c58b))

## [8.0.3](https://github.com/googleapis/teeny-request/compare/v8.0.2...v8.0.3) (2023-02-17)


### Bug Fixes

* Use pipeline in place of pipe to better handle cleanup / errors ([#299](https://github.com/googleapis/teeny-request/issues/299)) ([31d9002](https://github.com/googleapis/teeny-request/commit/31d900227d2ea7cb205fd4dc8db0aed5abd0dfa9))

## [8.0.2](https://github.com/googleapis/teeny-request/compare/v8.0.1...v8.0.2) (2022-09-09)


### Bug Fixes

* **deps:** Update dependency uuid to v9 ([#292](https://github.com/googleapis/teeny-request/issues/292)) ([f9fa756](https://github.com/googleapis/teeny-request/commit/f9fa75647913e2be10a48635168b11520d70982f))

## [8.0.1](https://github.com/googleapis/teeny-request/compare/v8.0.0...v8.0.1) (2022-08-23)


### Bug Fixes

* remove pip install statements ([#1546](https://github.com/googleapis/teeny-request/issues/1546)) ([#290](https://github.com/googleapis/teeny-request/issues/290)) ([94e7a86](https://github.com/googleapis/teeny-request/commit/94e7a86f13ab380399049e110c648cd381fb1e00))

## [8.0.0](https://github.com/googleapis/teeny-request/compare/v7.2.0...v8.0.0) (2022-05-09)


### ⚠ BREAKING CHANGES

* drop node 10, update typescript to 4.6.3 (#279)

### Build System

* drop node 10, update typescript to 4.6.3 ([#279](https://github.com/googleapis/teeny-request/issues/279)) ([6f12739](https://github.com/googleapis/teeny-request/commit/6f1273993ae667078abae41a8029833749a25e6a))

## [7.2.0](https://github.com/googleapis/teeny-request/compare/v7.1.3...v7.2.0) (2022-03-31)


### Features

* Add support for `NO_PROXY` ([#272](https://github.com/googleapis/teeny-request/issues/272)) ([b02b6e5](https://github.com/googleapis/teeny-request/commit/b02b6e515575814bbef7360586d07030b445705d))

### [7.1.3](https://www.github.com/googleapis/teeny-request/compare/v7.1.2...v7.1.3) (2021-09-24)


### Bug Fixes

* **deps:** update dependency http-proxy-agent to v5 ([#253](https://www.github.com/googleapis/teeny-request/issues/253)) ([c84dff4](https://www.github.com/googleapis/teeny-request/commit/c84dff45e4a0e71fc5205838dae51f57f3139c06))

### [7.1.2](https://www.github.com/googleapis/teeny-request/compare/v7.1.1...v7.1.2) (2021-09-10)


### Bug Fixes

* **build:** set default branch to main ([#247](https://www.github.com/googleapis/teeny-request/issues/247)) ([db3126d](https://www.github.com/googleapis/teeny-request/commit/db3126df77bc7e6cebb7d092001bbd3a444207d6))

### [7.1.1](https://www.github.com/googleapis/teeny-request/compare/v7.1.0...v7.1.1) (2021-06-30)


### Bug Fixes

* throw error if missing uri or url ([#239](https://www.github.com/googleapis/teeny-request/issues/239)) ([4d770e3](https://www.github.com/googleapis/teeny-request/commit/4d770e3b89254c4cec30c422cdcdad257500c9cc))

## [7.1.0](https://www.github.com/googleapis/teeny-request/compare/v7.0.1...v7.1.0) (2021-05-19)


### Features

* add `gcf-owl-bot[bot]` to `ignoreAuthors` ([#224](https://www.github.com/googleapis/teeny-request/issues/224)) ([3e7424f](https://www.github.com/googleapis/teeny-request/commit/3e7424fb63f0c0dca3606d6e1fc2f294f9d86ba5))


### Bug Fixes

* Buffer is allow as body without encoding to string ([#223](https://www.github.com/googleapis/teeny-request/issues/223)) ([d9bcdc3](https://www.github.com/googleapis/teeny-request/commit/d9bcdc36a60074fd78718ea8f7c4745d640e3b4f))

### [7.0.1](https://www.github.com/googleapis/teeny-request/compare/v7.0.0...v7.0.1) (2020-09-29)


### Bug Fixes

* **deps:** update node-fetch to 2.6.1 ([#200](https://www.github.com/googleapis/teeny-request/issues/200)) ([8958a78](https://www.github.com/googleapis/teeny-request/commit/8958a78b117e5610f3ccf121ba1d650dbe9739f8))

## [7.0.0](https://www.github.com/googleapis/teeny-request/compare/v6.0.3...v7.0.0) (2020-06-01)


### ⚠ BREAKING CHANGES

* dropping support for Node.js 8.x

### Features

* pass agent options when using agent pool config ([#149](https://www.github.com/googleapis/teeny-request/issues/149)) ([38ece79](https://www.github.com/googleapis/teeny-request/commit/38ece79151b667ec1a72ec50b1c7a58258924794))
* warn on too many concurrent requests ([#165](https://www.github.com/googleapis/teeny-request/issues/165)) ([88ff2d0](https://www.github.com/googleapis/teeny-request/commit/88ff2d0d8e0fc25a4219ef5625b8de353ed4aa29))


### Bug Fixes

* apache license URL ([#468](https://www.github.com/googleapis/teeny-request/issues/468)) ([#156](https://www.github.com/googleapis/teeny-request/issues/156)) ([01ac7bd](https://www.github.com/googleapis/teeny-request/commit/01ac7bd01e870796fd15355e079649633d5d5983))
* update template files for Node.js libraries ([#152](https://www.github.com/googleapis/teeny-request/issues/152)) ([89833c3](https://www.github.com/googleapis/teeny-request/commit/89833c3c3e8afea04c85a60811f122c5a6d37e48))
* **deps:** update dependency uuid to v8 ([#164](https://www.github.com/googleapis/teeny-request/issues/164)) ([2ab8155](https://www.github.com/googleapis/teeny-request/commit/2ab81550aeb8ca914516ff4ac20ebbb7b3d73fa5))


### Build System

* drop support for node.js 8.x ([#159](https://www.github.com/googleapis/teeny-request/issues/159)) ([d87aa73](https://www.github.com/googleapis/teeny-request/commit/d87aa73d3fafbdc013b03b7629a41decda6da98a))

### [6.0.3](https://www.github.com/googleapis/teeny-request/compare/v6.0.2...v6.0.3) (2020-03-06)


### Bug Fixes

* **deps:** update dependency uuid to v7 ([#134](https://www.github.com/googleapis/teeny-request/issues/134)) ([97817bf](https://www.github.com/googleapis/teeny-request/commit/97817bfb12396f620b2e280dcdc8965c4815abb5))

### [6.0.2](https://www.github.com/googleapis/teeny-request/compare/v6.0.1...v6.0.2) (2020-02-10)


### Bug Fixes

* **deps:** update dependency https-proxy-agent to v5 ([#128](https://www.github.com/googleapis/teeny-request/issues/128)) ([5dcef3f](https://www.github.com/googleapis/teeny-request/commit/5dcef3f5883b24a1092def38004074d04e37e241))

### [6.0.1](https://www.github.com/googleapis/teeny-request/compare/v6.0.0...v6.0.1) (2020-01-24)


### Bug Fixes

* **deps:** update dependency http-proxy-agent to v4 ([#121](https://www.github.com/googleapis/teeny-request/issues/121)) ([7caabcf](https://www.github.com/googleapis/teeny-request/commit/7caabcf154d8cf0848e443ce2cd4fbfae913ca41))

## [6.0.0](https://www.github.com/googleapis/teeny-request/compare/v5.3.3...v6.0.0) (2020-01-11)


### ⚠ BREAKING CHANGES

* remove console log and throw instead (#107)

### Bug Fixes

* remove console log and throw instead ([#107](https://www.github.com/googleapis/teeny-request/issues/107)) ([965beaa](https://www.github.com/googleapis/teeny-request/commit/965beaae17f0273992c9856ebf79b6f1befc59fe))

### [5.3.3](https://www.github.com/googleapis/teeny-request/compare/v5.3.2...v5.3.3) (2019-12-15)


### Bug Fixes

* **deps:** update dependency http-proxy-agent to v3 ([#104](https://www.github.com/googleapis/teeny-request/issues/104)) ([35a47d8](https://www.github.com/googleapis/teeny-request/commit/35a47d83adf92b16eab3fce52deae0e3c1353aa6))
* **deps:** update dependency https-proxy-agent to v4 ([#105](https://www.github.com/googleapis/teeny-request/issues/105)) ([26b67af](https://www.github.com/googleapis/teeny-request/commit/26b67afcb084ce1b99a62ecc55050d6f8f8aaee4))
* **docs:** add jsdoc-region-tag plugin ([#98](https://www.github.com/googleapis/teeny-request/issues/98)) ([8f3c35a](https://www.github.com/googleapis/teeny-request/commit/8f3c35aee711a1262ffa7c058eb1b9f18204b80e))

### [5.3.2](https://www.github.com/googleapis/teeny-request/compare/v5.3.1...v5.3.2) (2019-12-05)


### Bug Fixes

* **deps:** pin TypeScript below 3.7.0 ([#102](https://www.github.com/googleapis/teeny-request/issues/102)) ([c0b81e6](https://www.github.com/googleapis/teeny-request/commit/c0b81e6e7c1bb7e4a3e823c2e41692bc8ede0218))

### [5.3.1](https://www.github.com/googleapis/teeny-request/compare/v5.3.0...v5.3.1) (2019-10-29)


### Bug Fixes

* correctly set compress/gzip option when false ([#95](https://www.github.com/googleapis/teeny-request/issues/95)) ([72ef307](https://www.github.com/googleapis/teeny-request/commit/72ef307364de542af3ef8581572b1897fca2bcf4))

## [5.3.0](https://www.github.com/googleapis/teeny-request/compare/v5.2.1...v5.3.0) (2019-10-09)


### Bug Fixes

* **deps:** update dependency https-proxy-agent to v3 ([#89](https://www.github.com/googleapis/teeny-request/issues/89)) ([dfd52cc](https://www.github.com/googleapis/teeny-request/commit/dfd52cc))


### Features

* agent pooling ([#86](https://www.github.com/googleapis/teeny-request/issues/86)) ([b182f51](https://www.github.com/googleapis/teeny-request/commit/b182f51))

### [5.2.1](https://www.github.com/googleapis/teeny-request/compare/v5.2.0...v5.2.1) (2019-08-14)


### Bug Fixes

* **types:** make types less strict for method ([#76](https://www.github.com/googleapis/teeny-request/issues/76)) ([9f07e98](https://www.github.com/googleapis/teeny-request/commit/9f07e98))

## [5.2.0](https://www.github.com/googleapis/teeny-request/compare/v5.1.3...v5.2.0) (2019-08-13)


### Bug Fixes

* if scheme is http:// use an HTTP agent ([#75](https://www.github.com/googleapis/teeny-request/issues/75)) ([abdf846](https://www.github.com/googleapis/teeny-request/commit/abdf846))
* remove unused logging ([#71](https://www.github.com/googleapis/teeny-request/issues/71)) ([4cb4967](https://www.github.com/googleapis/teeny-request/commit/4cb4967))
* undefined headers breaks compatibility with auth ([#66](https://www.github.com/googleapis/teeny-request/issues/66)) ([12901a0](https://www.github.com/googleapis/teeny-request/commit/12901a0))


### Features

* support lazy-reading from response stream ([#74](https://www.github.com/googleapis/teeny-request/issues/74)) ([f6db420](https://www.github.com/googleapis/teeny-request/commit/f6db420))
* support reading from the request stream ([#67](https://www.github.com/googleapis/teeny-request/issues/67)) ([ae23054](https://www.github.com/googleapis/teeny-request/commit/ae23054))


### Reverts

* do not pipe fetch response into user stream ([#72](https://www.github.com/googleapis/teeny-request/issues/72)) ([6ec812e](https://www.github.com/googleapis/teeny-request/commit/6ec812e))

### [5.1.3](https://www.github.com/googleapis/teeny-request/compare/v5.1.2...v5.1.3) (2019-08-06)


### Bug Fixes

* duplex stream does not implement methods like _read ([#64](https://www.github.com/googleapis/teeny-request/issues/64)) ([22ee26c](https://www.github.com/googleapis/teeny-request/commit/22ee26c))

### [5.1.2](https://www.github.com/googleapis/teeny-request/compare/v5.1.1...v5.1.2) (2019-08-06)


### Bug Fixes

* **types:** expand method and header types ([#61](https://www.github.com/googleapis/teeny-request/issues/61)) ([c04d2f1](https://www.github.com/googleapis/teeny-request/commit/c04d2f1))

### [5.1.1](https://www.github.com/googleapis/teeny-request/compare/v5.1.0...v5.1.1) (2019-07-23)


### Bug Fixes

* support lowercase proxy env vars ([#56](https://www.github.com/googleapis/teeny-request/issues/56)) ([0b3e433](https://www.github.com/googleapis/teeny-request/commit/0b3e433))

## [5.1.0](https://www.github.com/googleapis/teeny-request/compare/v5.0.0...v5.1.0) (2019-07-19)


### Features

* support forever option ([#54](https://www.github.com/googleapis/teeny-request/issues/54)) ([746d70e](https://www.github.com/googleapis/teeny-request/commit/746d70e))

## [5.0.0](https://www.github.com/googleapis/teeny-request/compare/v4.0.0...v5.0.0) (2019-07-15)


### ⚠ BREAKING CHANGES

* this is our first release since moving into googleapis org; in the theme of "better safe than sorry" we're releasing as 5.0.0.

### Bug Fixes

* export types independent of @types/request ([#44](https://www.github.com/googleapis/teeny-request/issues/44)) ([fbe2b77](https://www.github.com/googleapis/teeny-request/commit/fbe2b77))


### Reverts

* revert 4.0.0 release in favor of 5.0.0 release ([#52](https://www.github.com/googleapis/teeny-request/issues/52)) ([f24499e](https://www.github.com/googleapis/teeny-request/commit/f24499e))
