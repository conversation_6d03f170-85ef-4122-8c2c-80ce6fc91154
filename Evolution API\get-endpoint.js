// Script para obter a URL do endpoint ngrok
const http = require('http');

async function getNgrokUrl() {
  return new Promise((resolve, reject) => {
    const req = http.get('http://localhost:4040/api/tunnels', (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          const tunnel = response.tunnels.find(t => t.proto === 'https');
          
          if (tunnel) {
            resolve(tunnel.public_url);
          } else {
            reject(new Error('Nenhum túnel HTTPS encontrado'));
          }
        } catch (error) {
          reject(error);
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      reject(new Error('Timeout ao conectar com ngrok'));
    });
  });
}

async function main() {
  try {
    console.log('🔍 Buscando URL do túnel ngrok...');
    
    const url = await getNgrokUrl();
    
    console.log('');
    console.log('🎉 ENCONTRADO! Seu endpoint público é:');
    console.log('');
    console.log(`🌐 URL BASE: ${url}`);
    console.log('');
    console.log('📋 SEUS ENDPOINTS PÚBLICOS:');
    console.log(`   ${url}/health          ← Status do servidor`);
    console.log(`   ${url}/ga4             ← Consulta GA4`);
    console.log(`   ${url}/ga4/llm         ← Dados para LLM`);
    console.log(`   ${url}/ga4/metrics     ← Lista métricas`);
    console.log(`   ${url}/ga4/dimensions  ← Lista dimensões`);
    console.log('');
    console.log('🧪 TESTE RÁPIDO:');
    console.log(`   curl ${url}/health`);
    console.log('');
    console.log('🤖 PARA LLMs:');
    console.log(`   Use a URL base: ${url}`);
    console.log('');
    
  } catch (error) {
    console.log('❌ Erro ao obter URL do ngrok:');
    console.log(`   ${error.message}`);
    console.log('');
    console.log('💡 Certifique-se de que:');
    console.log('   1. O túnel ngrok está rodando');
    console.log('   2. A interface web está em http://localhost:4040');
    console.log('');
    console.log('🚀 Para iniciar o túnel:');
    console.log('   node start-ngrok.js');
    console.log('');
  }
}

main();
