// Script usando localtunnel como alternativa ao ngrok
const { spawn } = require('child_process');

async function criarTunnel() {
  try {
    console.log('🚀 Iniciando túnel público para GA4 API');
    console.log('');
    console.log('🌐 Usando LocalTunnel (alternativa ao ngrok)');
    console.log('');
    console.log('⚙️ Criando túnel na porta 3000...');
    console.log('');
    console.log('⚠️  IMPORTANTE: Você verá uma URL como:');
    console.log('   https://abc123.loca.lt');
    console.log('');
    console.log('📋 Essa será a URL base dos seus endpoints:');
    console.log('   https://abc123.loca.lt/health');
    console.log('   https://abc123.loca.lt/ga4');
    console.log('   https://abc123.loca.lt/ga4/llm');
    console.log('   https://abc123.loca.lt/ga4/metrics');
    console.log('   https://abc123.loca.lt/ga4/dimensions');
    console.log('');
    console.log('🛑 Para parar, pressione Ctrl+C');
    console.log('');
    console.log('═'.repeat(60));
    console.log('');
    
    // Iniciar localtunnel
    const tunnelProcess = spawn('npx', ['localtunnel', '--port', '3000'], {
      stdio: 'inherit',
      shell: true
    });
    
    // Capturar Ctrl+C
    process.on('SIGINT', () => {
      console.log('');
      console.log('🛑 Parando túnel...');
      tunnelProcess.kill();
      process.exit(0);
    });
    
    tunnelProcess.on('error', (error) => {
      console.error('❌ Erro ao iniciar túnel:', error.message);
      console.log('');
      console.log('💡 Tente instalar localtunnel:');
      console.log('   npm install -g localtunnel');
      console.log('');
      process.exit(1);
    });
    
    tunnelProcess.on('close', (code) => {
      console.log('');
      console.log('🛑 Túnel finalizado');
      process.exit(code);
    });
    
  } catch (error) {
    console.error('');
    console.error('❌ Erro:', error.message);
    process.exit(1);
  }
}

// Verificar se o servidor está rodando
function verificarServidor() {
  return new Promise((resolve) => {
    const net = require('net');
    const client = new net.Socket();
    
    client.setTimeout(1000);
    
    client.on('connect', () => {
      client.destroy();
      resolve(true);
    });
    
    client.on('error', () => {
      resolve(false);
    });
    
    client.on('timeout', () => {
      client.destroy();
      resolve(false);
    });
    
    client.connect(3000, 'localhost');
  });
}

async function main() {
  console.log('🔍 Verificando servidor na porta 3000...');
  
  const servidorRodando = await verificarServidor();
  
  if (!servidorRodando) {
    console.log('❌ Servidor não está rodando na porta 3000');
    console.log('');
    console.log('🚀 Inicie o servidor primeiro:');
    console.log('   npm start');
    console.log('');
    console.log('📋 Depois execute este script:');
    console.log('   node tunnel-alternativo.js');
    console.log('');
    process.exit(1);
  }
  
  console.log('✅ Servidor detectado na porta 3000');
  console.log('');
  
  await criarTunnel();
}

main();
