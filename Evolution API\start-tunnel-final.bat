@echo off
echo 🚀 Iniciando Servidor GA4 + Túnel ngrok
echo.

REM Verificar se o servidor está rodando
echo 📊 Verificando servidor na porta 3000...
netstat -an | find "3000" | find "LISTENING" >nul
if %errorlevel% neq 0 (
    echo ❌ Servidor não está rodando na porta 3000
    echo.
    echo 🚀 Iniciando servidor...
    start /B npm start
    echo ⏳ Aguardando servidor inicializar...
    timeout /t 5 /nobreak >nul
) else (
    echo ✅ Servidor já está rodando na porta 3000
)

echo.
echo 🌐 Iniciando túnel ngrok...
echo.
echo ⚠️  IMPORTANTE: Você verá uma URL como:
echo    https://abc123.ngrok.io
echo.
echo 📋 Seus endpoints públicos serão:
echo    https://abc123.ngrok.io/health
echo    https://abc123.ngrok.io/ga4
echo    https://abc123.ngrok.io/ga4/llm
echo    https://abc123.ngrok.io/ga4/metrics
echo    https://abc123.ngrok.io/ga4/dimensions
echo.
echo 🔗 Interface web: http://localhost:4040
echo.
echo ═══════════════════════════════════════════════════
echo.

REM Tentar usar ngrok local primeiro
if exist "node_modules\.bin\ngrok.cmd" (
    echo 📦 Usando ngrok local...
    node_modules\.bin\ngrok.cmd http 3000
) else (
    echo 🌐 Tentando ngrok global...
    ngrok http 3000
)
