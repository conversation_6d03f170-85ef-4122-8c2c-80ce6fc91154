{"name": "evolution-api", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "NODE_ENV=development node server.js", "test": "node test-api.js", "test:health": "curl http://localhost:3000/health", "test:metrics": "curl http://localhost:3000/ga4/metrics", "tunnel": "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Ngrok.Ngrok_Microsoft.Winget.Source_8wekyb3d8bbwe\\ngrok.exe http 3000", "tunnel:auth": "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Ngrok.Ngrok_Microsoft.Winget.Source_8wekyb3d8bbwe\\ngrok.exe http 3000 --authtoken YOUR_NGROK_TOKEN", "start:tunnel": "concurrently \"npm start\" \"C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Ngrok.Ngrok_Microsoft.Winget.Source_8wekyb3d8bbwe\\ngrok.exe http 3000\""}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@google-analytics/data": "^5.1.0", "@ngrok/ngrok": "^1.5.1", "body-parser": "^2.2.0", "express": "^5.1.0", "ngrok": "^5.0.0-beta.2"}, "devDependencies": {"concurrently": "^9.2.0"}}