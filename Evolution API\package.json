{"name": "evolution-api", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "NODE_ENV=development node server.js", "test": "node test-api.js", "test:health": "curl http://localhost:3000/health", "test:metrics": "curl http://localhost:3000/ga4/metrics"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@google-analytics/data": "^5.1.0", "body-parser": "^2.2.0", "express": "^5.1.0"}}