# Script PowerShell para configurar ngrok authtoken
Write-Host "🔑 Configurador do ngrok Authtoken" -ForegroundColor Green
Write-Host ""

Write-Host "📋 PASSO 1: Obter seu token" -ForegroundColor Cyan
Write-Host "   1. Acesse: https://dashboard.ngrok.com/signup" -ForegroundColor White
Write-Host "   2. Faça login (ou crie conta gratuita)" -ForegroundColor White
Write-Host "   3. Vá para: https://dashboard.ngrok.com/get-started/your-authtoken" -ForegroundColor White
Write-Host "   4. Copie o token que aparece" -ForegroundColor White
Write-Host ""
Write-Host "💡 O token é algo como: 2abc123def456ghi789jkl" -ForegroundColor Yellow
Write-Host ""

# Abrir automaticamente a página do token
$openPage = Read-Host "🌐 Deseja abrir a página do token automaticamente? (s/n)"
if ($openPage -eq "s" -or $openPage -eq "S") {
    Start-Process "https://dashboard.ngrok.com/get-started/your-authtoken"
    Write-Host "🌐 Página aberta no navegador!" -ForegroundColor Green
    Write-Host ""
}

# Solicitar token
$token = Read-Host "🔑 Cole seu token aqui e pressione Enter"

if ([string]::IsNullOrWhiteSpace($token)) {
    Write-Host "❌ Token não pode estar vazio!" -ForegroundColor Red
    Read-Host "Pressione Enter para sair"
    exit 1
}

Write-Host ""
Write-Host "⚙️ Configurando token..." -ForegroundColor Yellow

# Caminho do ngrok
$ngrokPath = "C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Ngrok.Ngrok_Microsoft.Winget.Source_8wekyb3d8bbwe\ngrok.exe"

# Verificar se ngrok existe
if (-not (Test-Path $ngrokPath)) {
    Write-Host "❌ Erro: ngrok não encontrado!" -ForegroundColor Red
    Write-Host "Instale com: winget install ngrok" -ForegroundColor Yellow
    Read-Host "Pressione Enter para sair"
    exit 1
}

# Configurar token
try {
    $result = & $ngrokPath authtoken $token 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "✅ Token configurado com sucesso!" -ForegroundColor Green
        Write-Host ""
        Write-Host "🚀 Agora você pode usar:" -ForegroundColor Cyan
        Write-Host "   .\start-tunnel.bat" -ForegroundColor White
        Write-Host "   .\start-tunnel.ps1" -ForegroundColor White
        Write-Host "   npm run tunnel" -ForegroundColor White
        Write-Host ""
        Write-Host "🧪 Testar configuração:" -ForegroundColor Cyan
        Write-Host "   npm start (em um terminal)" -ForegroundColor White
        Write-Host "   npm run tunnel (em outro terminal)" -ForegroundColor White
        Write-Host ""
        
        # Perguntar se quer testar agora
        $test = Read-Host "🧪 Deseja testar o túnel agora? (s/n)"
        if ($test -eq "s" -or $test -eq "S") {
            Write-Host ""
            Write-Host "🚀 Iniciando teste..." -ForegroundColor Green
            Write-Host "📊 Primeiro, vamos iniciar o servidor..." -ForegroundColor Cyan
            
            # Iniciar servidor em background
            $serverJob = Start-Job -ScriptBlock {
                Set-Location $using:PWD
                npm start
            }
            
            Start-Sleep -Seconds 3
            
            Write-Host "🌐 Agora iniciando o túnel ngrok..." -ForegroundColor Cyan
            Write-Host "⚠️  Pressione Ctrl+C para parar" -ForegroundColor Yellow
            Write-Host ""
            
            try {
                & $ngrokPath http 3000
            }
            finally {
                Write-Host ""
                Write-Host "🧹 Parando servidor..." -ForegroundColor Yellow
                Stop-Job $serverJob -ErrorAction SilentlyContinue
                Remove-Job $serverJob -ErrorAction SilentlyContinue
                Write-Host "✅ Teste finalizado" -ForegroundColor Green
            }
        }
        
    } else {
        Write-Host ""
        Write-Host "❌ Erro ao configurar token!" -ForegroundColor Red
        Write-Host "Verifique se o token está correto." -ForegroundColor Yellow
        Write-Host "Resultado: $result" -ForegroundColor Red
        Write-Host ""
    }
} catch {
    Write-Host ""
    Write-Host "❌ Erro ao executar ngrok!" -ForegroundColor Red
    Write-Host "Erro: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
}

Read-Host "Pressione Enter para sair"
