// Script final para criar túnel ngrok
const ngrok = require('ngrok');

async function criarTunnel() {
  try {
    console.log('🚀 Iniciando túnel ngrok...');
    console.log('');
    
    // Configurar authtoken se necessário
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    console.log('🔑 CONFIGURAÇÃO DO NGROK');
    console.log('');
    console.log('📋 Se ainda não configurou:');
    console.log('1. Acesse: https://dashboard.ngrok.com/get-started/your-authtoken');
    console.log('2. Copie seu authtoken');
    console.log('');
    
    const token = await new Promise((resolve) => {
      rl.question('🔑 Cole seu authtoken (ou Enter se j<PERSON> configurou): ', (answer) => {
        rl.close();
        resolve(answer.trim());
      });
    });

    console.log('');
    console.log('⚙️ Criando túnel...');

    // Configurar ngrok
    const options = {
      addr: 3000,
      region: 'us'
    };

    if (token) {
      options.authtoken = token;
    }

    // Criar túnel
    const url = await ngrok.connect(options);
    
    console.log('');
    console.log('🎉 TÚNEL CRIADO COM SUCESSO!');
    console.log('');
    console.log('🌐 URL PÚBLICA DO SEU ENDPOINT:');
    console.log(`   ${url}`);
    console.log('');
    console.log('📋 SEUS ENDPOINTS PÚBLICOS:');
    console.log(`   ${url}/health          ← Status do servidor`);
    console.log(`   ${url}/ga4             ← Consulta GA4`);
    console.log(`   ${url}/ga4/llm         ← Dados para LLM`);
    console.log(`   ${url}/ga4/metrics     ← Lista métricas`);
    console.log(`   ${url}/ga4/dimensions  ← Lista dimensões`);
    console.log('');
    console.log('🧪 TESTE RÁPIDO:');
    console.log(`   curl ${url}/health`);
    console.log('');
    console.log('🤖 PARA LLMs:');
    console.log(`   Use a URL base: ${url}`);
    console.log('');
    console.log('🔗 Interface web: http://localhost:4040');
    console.log('');
    console.log('🛑 Para parar, pressione Ctrl+C');
    console.log('');
    
    // Manter o processo rodando
    process.on('SIGINT', async () => {
      console.log('');
      console.log('🛑 Parando túnel ngrok...');
      await ngrok.disconnect();
      await ngrok.kill();
      console.log('✅ Túnel finalizado');
      process.exit(0);
    });
    
    // Manter vivo
    console.log('═'.repeat(60));
    console.log('TÚNEL ATIVO - Pressione Ctrl+C para parar');
    console.log('═'.repeat(60));
    
    // Loop infinito para manter o processo vivo
    setInterval(() => {
      // Apenas para manter rodando
    }, 1000);
    
  } catch (error) {
    console.error('');
    console.error('❌ Erro ao criar túnel ngrok:');
    console.error(`   ${error.message}`);
    console.error('');
    
    if (error.message.includes('authtoken')) {
      console.log('🔑 Configure seu authtoken:');
      console.log('1. Acesse: https://dashboard.ngrok.com/get-started/your-authtoken');
      console.log('2. Copie seu token');
      console.log('3. Execute este script novamente');
      console.log('');
    }
    
    process.exit(1);
  }
}

// Verificar se o servidor está rodando
const net = require('net');

function verificarServidor() {
  return new Promise((resolve) => {
    const client = new net.Socket();
    
    client.setTimeout(1000);
    
    client.on('connect', () => {
      client.destroy();
      resolve(true);
    });
    
    client.on('error', () => {
      resolve(false);
    });
    
    client.on('timeout', () => {
      client.destroy();
      resolve(false);
    });
    
    client.connect(3000, 'localhost');
  });
}

async function main() {
  console.log('🔍 Verificando servidor na porta 3000...');
  
  const servidorRodando = await verificarServidor();
  
  if (!servidorRodando) {
    console.log('❌ Servidor não está rodando na porta 3000');
    console.log('');
    console.log('🚀 Inicie o servidor primeiro:');
    console.log('   npm start');
    console.log('');
    console.log('📋 Depois execute este script:');
    console.log('   node criar-tunnel.js');
    console.log('');
    process.exit(1);
  }
  
  console.log('✅ Servidor detectado na porta 3000');
  console.log('');
  
  await criarTunnel();
}

main();
