// Arquivo de teste para a API GA4
// Execute com: node test-api.js

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// Função para testar o endpoint de health
async function testHealth() {
  try {
    console.log('🔍 Testando endpoint /health...');
    const response = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health check:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Erro no health check:', error.message);
    return false;
  }
}

// Função para testar listagem de métricas
async function testMetrics() {
  try {
    console.log('\n📊 Testando endpoint /ga4/metrics...');
    const response = await axios.get(`${BASE_URL}/ga4/metrics`);
    console.log('✅ Métricas disponíveis:', response.data.metrics.slice(0, 5), '...');
    return true;
  } catch (error) {
    console.error('❌ Erro ao buscar métricas:', error.message);
    return false;
  }
}

// Função para testar listagem de dimensões
async function testDimensions() {
  try {
    console.log('\n📏 Testando endpoint /ga4/dimensions...');
    const response = await axios.get(`${BASE_URL}/ga4/dimensions`);
    console.log('✅ Dimensões disponíveis:', response.data.dimensions.slice(0, 5), '...');
    return true;
  } catch (error) {
    console.error('❌ Erro ao buscar dimensões:', error.message);
    return false;
  }
}

// Função para testar consulta GA4 (vai falhar sem credenciais válidas)
async function testGA4Query() {
  try {
    console.log('\n🔍 Testando endpoint /ga4...');
    const response = await axios.post(`${BASE_URL}/ga4`, {
      propertyId: 'properties/SEU_PROPERTY_ID',
      startDate: '2024-06-01',
      endDate: '2024-06-28',
      metrics: ['sessions', 'users'],
      dimensions: ['country'],
      limit: 5
    });
    console.log('✅ Consulta GA4 bem-sucedida:', response.data.summary);
    return true;
  } catch (error) {
    console.log('⚠️  Consulta GA4 falhou (esperado sem credenciais válidas):', error.response?.data?.message || error.message);
    return false;
  }
}

// Função para testar endpoint LLM
async function testLLMEndpoint() {
  try {
    console.log('\n🤖 Testando endpoint /ga4/llm...');
    const response = await axios.post(`${BASE_URL}/ga4/llm`, {
      propertyId: 'properties/SEU_PROPERTY_ID',
      startDate: '2024-06-01',
      endDate: '2024-06-28',
      query: 'Forneça um resumo dos dados de tráfego'
    });
    console.log('✅ Endpoint LLM bem-sucedido:', response.data.period);
    return true;
  } catch (error) {
    console.log('⚠️  Endpoint LLM falhou (esperado sem credenciais válidas):', error.response?.data?.message || error.message);
    return false;
  }
}

// Função principal para executar todos os testes
async function runTests() {
  console.log('🚀 Iniciando testes da API GA4...\n');
  
  const results = {
    health: await testHealth(),
    metrics: await testMetrics(),
    dimensions: await testDimensions(),
    ga4Query: await testGA4Query(),
    llmEndpoint: await testLLMEndpoint()
  };
  
  console.log('\n📋 Resumo dos Testes:');
  console.log('Health Check:', results.health ? '✅' : '❌');
  console.log('Métricas:', results.metrics ? '✅' : '❌');
  console.log('Dimensões:', results.dimensions ? '✅' : '❌');
  console.log('Consulta GA4:', results.ga4Query ? '✅' : '⚠️');
  console.log('Endpoint LLM:', results.llmEndpoint ? '✅' : '⚠️');
  
  const basicTests = results.health && results.metrics && results.dimensions;
  
  if (basicTests) {
    console.log('\n🎉 Servidor está funcionando corretamente!');
    console.log('💡 Para testar as consultas GA4, configure as credenciais e Property ID corretos.');
  } else {
    console.log('\n❌ Alguns testes básicos falharam. Verifique se o servidor está rodando.');
  }
}

// Executar testes se o arquivo for chamado diretamente
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests, testHealth, testMetrics, testDimensions, testGA4Query, testLLMEndpoint };
