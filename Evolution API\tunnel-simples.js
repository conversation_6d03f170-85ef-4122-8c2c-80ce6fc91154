// Script simples para criar túnel ngrok
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Salvar token em arquivo local
function saveToken(token) {
  const configPath = path.join(__dirname, '.ngrok-token');
  fs.writeFileSync(configPath, token.trim());
  console.log('✅ Token salvo localmente');
}

// Ler token do arquivo
function getToken() {
  const configPath = path.join(__dirname, '.ngrok-token');
  if (fs.existsSync(configPath)) {
    return fs.readFileSync(configPath, 'utf8').trim();
  }
  return null;
}

// Solicitar token do usuário
function askForToken() {
  return new Promise((resolve) => {
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    console.log('🔑 CONFIGURAÇÃO DO NGROK');
    console.log('');
    console.log('📋 Se ainda não fez:');
    console.log('1. Acesse: https://dashboard.ngrok.com/get-started/your-authtoken');
    console.log('2. Copie seu authtoken');
    console.log('');
    
    rl.question('🔑 Cole seu authtoken aqui (ou Enter se já configurou): ', (token) => {
      rl.close();
      resolve(token.trim());
    });
  });
}

async function main() {
  console.log('🚀 Iniciando túnel ngrok simples...');
  console.log('');

  // Verificar se já temos token salvo
  let token = getToken();
  
  if (!token) {
    token = await askForToken();
    if (token) {
      saveToken(token);
    }
  } else {
    console.log('✅ Token encontrado no arquivo local');
  }

  console.log('');
  console.log('🌐 Criando túnel para porta 3000...');
  console.log('');
  console.log('⚠️  IMPORTANTE: Quando o túnel iniciar, você verá uma URL como:');
  console.log('   https://abc123.ngrok.io');
  console.log('');
  console.log('📋 Essa será a URL base dos seus endpoints:');
  console.log('   https://abc123.ngrok.io/health');
  console.log('   https://abc123.ngrok.io/ga4');
  console.log('   https://abc123.ngrok.io/ga4/llm');
  console.log('');
  console.log('🛑 Para parar, pressione Ctrl+C');
  console.log('');
  console.log('═'.repeat(50));
  console.log('');

  // Usar ngrok via npm (mais confiável)
  const ngrok = spawn('npx', ['ngrok', 'http', '3000'], {
    stdio: 'inherit',
    shell: true
  });

  ngrok.on('error', (error) => {
    console.error('❌ Erro ao iniciar ngrok:', error.message);
    console.log('');
    console.log('💡 Tente instalar o ngrok globalmente:');
    console.log('   npm install -g ngrok');
    console.log('');
  });

  ngrok.on('close', (code) => {
    console.log('');
    console.log('🛑 Túnel ngrok finalizado');
  });

  // Capturar Ctrl+C
  process.on('SIGINT', () => {
    console.log('');
    console.log('🛑 Parando túnel...');
    ngrok.kill();
    process.exit(0);
  });
}

// Verificar se servidor está rodando
const net = require('net');
const server = net.createServer();

server.listen(3000, (err) => {
  if (err) {
    console.log('✅ Servidor detectado na porta 3000');
    server.close();
    main();
  } else {
    console.log('❌ Nenhum servidor encontrado na porta 3000');
    console.log('');
    console.log('🚀 Inicie o servidor primeiro:');
    console.log('   npm start');
    console.log('');
    server.close();
    process.exit(1);
  }
});

server.on('error', (err) => {
  if (err.code === 'EADDRINUSE') {
    console.log('✅ Servidor detectado na porta 3000');
    main();
  } else {
    console.error('❌ Erro ao verificar porta 3000:', err.message);
    process.exit(1);
  }
});
