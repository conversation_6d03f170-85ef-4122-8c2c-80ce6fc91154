name: build

on: [push, pull_request]

jobs:
  test:
    name: Test
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        node-version: [10.x, 12.x, 14.x]
        os: [ubuntu-latest, windows-latest, macOS-latest]

    steps:
    - uses: actions/checkout@v2

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v1
      with:
        node-version: ${{ matrix.node-version }}

    - name: Install
      run: |
        npm install

    - name: Test
      run: |
        npm test
