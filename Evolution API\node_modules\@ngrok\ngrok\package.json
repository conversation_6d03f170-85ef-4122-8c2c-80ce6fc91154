{"name": "@ngrok/ngrok", "version": "1.5.1", "main": "index.js", "types": "index.d.ts", "files": ["CHANGELOG.md", "CODE_OF_CONDUCT.md", "CONTRIBUTING.md", "LICENSE-APACHE", "LICENSE-MIT", "README.md", "index.js", "index.d.ts", "package.json"], "napi": {"name": "ngrok", "triples": {"additional": ["aarch64-apple-darwin", "aarch64-linux-android", "aarch64-pc-windows-msvc", "aarch64-unknown-linux-gnu", "aarch64-unknown-linux-musl", "armv7-unknown-linux-gnueabihf", "x86_64-unknown-linux-musl", "x86_64-unknown-freebsd", "i686-pc-windows-msvc", "universal-apple-darwin"]}}, "license": "(MIT OR Apache-2.0)", "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "@napi-rs/cli": "^2.18.4", "@types/node": "^22.13.0", "ava": "^6.2.0", "axios": "^1.8.3", "axios-retry": "^4.5.0", "babel-jest": "^29.7.0", "expect": "^29.7.0", "express": "^4.21.2", "jest": "^30.0.0-alpha.6", "prettier": "^3.5.3", "typedoc": "^0.28.0", "typescript": "^5.8.2"}, "ava": {"timeout": "3m"}, "engines": {"node": ">= 10"}, "scripts": {"artifacts": "napi artifacts", "build": "napi build --platform --release --js gen.js --dts gen.d.ts --pipe 'node post-build.js trailer index'", "build:debug": "napi build --platform", "clean": "rm -rf node_modules/ target/", "docs": "mv trailer.d.ts trailer.d.hidden; mv examples .examples; npx typedoc index.d.ts --navigation.includeGroups; mv .examples examples; mv trailer.d.hidden trailer.d.ts", "prepublishOnly": "napi prepublish -t npm", "prettier": "prettier --write .", "test": "jest", "universal": "napi universal", "version": "napi version"}, "packageManager": "yarn@3.3.1", "description": "The ngrok agent in library form, suitable for integrating directly into your NodeJS application.", "keywords": ["ngrok", "nodejs", "ingress", "networking"], "homepage": "https://ngrok.github.io/ngrok-javascript/", "repository": {"type": "git", "url": "https://github.com/ngrok/ngrok-javascript.git"}, "jest": {"testTimeout": 30000, "testMatch": ["**/__test__/**/*.mjs"], "testPathIgnorePatterns": ["/__test__/retry-config.mjs$"], "transform": {"^.+\\.(mjs)$": "babel-jest"}, "moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}}, "optionalDependencies": {"@ngrok/ngrok-win32-x64-msvc": "1.5.1", "@ngrok/ngrok-darwin-x64": "1.5.1", "@ngrok/ngrok-linux-x64-gnu": "1.5.1", "@ngrok/ngrok-darwin-arm64": "1.5.1", "@ngrok/ngrok-android-arm64": "1.5.1", "@ngrok/ngrok-win32-arm64-msvc": "1.5.1", "@ngrok/ngrok-linux-arm64-gnu": "1.5.1", "@ngrok/ngrok-linux-arm64-musl": "1.5.1", "@ngrok/ngrok-linux-arm-gnueabihf": "1.5.1", "@ngrok/ngrok-linux-x64-musl": "1.5.1", "@ngrok/ngrok-freebsd-x64": "1.5.1", "@ngrok/ngrok-win32-ia32-msvc": "1.5.1", "@ngrok/ngrok-darwin-universal": "1.5.1"}}