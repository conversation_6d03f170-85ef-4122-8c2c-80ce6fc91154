{"name": "ngrok", "version": "5.0.0-beta.2", "description": "node wrapper for ngrok", "main": "index.js", "exports": {".": "./index.js", "./download": "./download.js"}, "types": "index.d.ts", "scripts": {"test": "mocha --exit", "postinstall": "node ./postinstall.js", "postupdate": "node ./postinstall.js"}, "files": ["bin/ngrok", "download.js", "download.d.ts", "index.js", "index.d.ts", "postinstall.js", "src/authtoken.js", "src/client.js", "src/config.js", "src/constants.js", "src/process.js", "src/utils.js", "src/version.js"], "repository": {"type": "git", "url": "git://github.com/bubenshchykov/ngrok.git"}, "keywords": ["ngrok", "localhost", "tunneling", "localtunnel", "webhook"], "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/bubenshchykov/ngrok/issues"}, "devDependencies": {"@types/node": "^14.18.24", "chai": "^4.3.7", "chalk": "^4.1.2", "mocha": "^10.2.0", "sinon": "^13.0.1"}, "dependencies": {"extract-zip": "^2.0.1", "got": "^11.8.5", "lodash.clonedeep": "^4.5.0", "uuid": "^7.0.0 || ^8.0.0", "yaml": "^2.2.2"}, "bin": {"ngrok": "bin/ngrok"}, "engines": {"node": ">=14.2"}, "optionalDependencies": {"hpagent": "^0.1.2"}}