@echo off
echo 🚀 Iniciando servidor GA4 API e túnel ngrok...
echo.

REM Iniciar o servidor Node.js em background
echo 📊 Iniciando servidor na porta 3000...
start /B npm start

REM Aguardar alguns segundos para o servidor inicializar
timeout /t 3 /nobreak >nul

REM Iniciar o túnel ngrok
echo 🌐 Iniciando túnel ngrok...
echo.
echo ⚠️  IMPORTANTE: Após iniciar o ngrok, você verá uma URL como:
echo    https://abc123.ngrok.io
echo.
echo 📋 Seus endpoints públicos serão:
echo    https://abc123.ngrok.io/health
echo    https://abc123.ngrok.io/ga4
echo    https://abc123.ngrok.io/ga4/llm
echo    https://abc123.ngrok.io/ga4/metrics
echo    https://abc123.ngrok.io/ga4/dimensions
echo.
echo 🔗 Interface web do ngrok: http://localhost:4040
echo.

C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Ngrok.Ngrok_Microsoft.Winget.Source_8wekyb3d8bbwe\ngrok.exe http 3000
