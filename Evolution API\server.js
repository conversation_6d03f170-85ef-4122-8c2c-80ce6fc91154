const express = require('express');
const {BetaAnalyticsDataClient} = require('@google-analytics/data');
const bodyParser = require('body-parser');
const app = express();
const port = process.env.PORT || 3000;

// Configuração do cliente GA4
const client = new BetaAnalyticsDataClient({
  keyFilename: './projeto-ga4-e-ia-b2180899d811.json'
});

// Middleware
app.use(bodyParser.json());
app.use(express.static('public')); // Para servir arquivos estáticos se necessário

// CORS para permitir requisições de diferentes origens
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  next();
});

// Endpoint principal para consultar dados do GA4
app.post('/ga4', async (req, res) => {
  try {
    // Parâmetros do body da requisição
    const {
      propertyId = 'properties/SEU_PROPERTY_ID',
      startDate = '2024-06-01',
      endDate = '2024-06-28',
      metrics = ['sessions'],
      dimensions = [],
      limit = 10
    } = req.body;

    // Configuração da consulta
    const request = {
      property: propertyId,
      dateRanges: [{ startDate, endDate }],
      metrics: metrics.map(metric => ({ name: metric })),
      limit: parseInt(limit)
    };

    // Adiciona dimensões se fornecidas
    if (dimensions.length > 0) {
      request.dimensions = dimensions.map(dimension => ({ name: dimension }));
    }

    const [report] = await client.runReport(request);

    // Formata os dados para facilitar o uso com LLM
    const formattedData = {
      summary: {
        totalRows: report.rowCount,
        dateRange: { startDate, endDate },
        metrics: metrics,
        dimensions: dimensions
      },
      data: report.rows?.map(row => {
        const result = {};

        // Adiciona valores das dimensões
        if (dimensions.length > 0) {
          dimensions.forEach((dimension, index) => {
            result[dimension] = row.dimensionValues[index]?.value || 'N/A';
          });
        }

        // Adiciona valores das métricas
        metrics.forEach((metric, index) => {
          result[metric] = row.metricValues[index]?.value || '0';
        });

        return result;
      }) || [],
      llmSummary: generateLLMSummary(report, metrics, dimensions, startDate, endDate)
    };

    res.json(formattedData);
  } catch (err) {
    console.error('Erro ao consultar GA4:', err);
    res.status(500).json({
      error: 'Erro ao consultar GA4',
      message: err.message,
      details: process.env.NODE_ENV === 'development' ? err.stack : undefined
    });
  }
});

// Função auxiliar para gerar resumo para LLM
function generateLLMSummary(report, metrics, dimensions, startDate, endDate) {
  const totalRows = report.rowCount || 0;
  const firstRow = report.rows?.[0];

  let summary = `Dados do Google Analytics 4 para o período de ${startDate} a ${endDate}:\n`;

  if (totalRows === 0) {
    summary += "Nenhum dado encontrado para os critérios especificados.";
    return summary;
  }

  summary += `Total de ${totalRows} registros encontrados.\n`;

  if (firstRow) {
    summary += "Principais métricas:\n";
    metrics.forEach((metric, index) => {
      const value = firstRow.metricValues[index]?.value || '0';
      summary += `- ${metric}: ${value}\n`;
    });

    if (dimensions.length > 0) {
      summary += "Dimensões analisadas: " + dimensions.join(', ') + "\n";
    }
  }

  return summary;
}

// Endpoint para listar métricas disponíveis
app.get('/ga4/metrics', (req, res) => {
  const commonMetrics = [
    'sessions', 'users', 'newUsers', 'pageviews', 'screenPageViews',
    'bounceRate', 'sessionDuration', 'averageSessionDuration',
    'conversions', 'totalRevenue', 'purchaseRevenue',
    'eventCount', 'engagementRate', 'engagedSessions'
  ];

  res.json({
    metrics: commonMetrics,
    description: "Lista de métricas comuns do GA4 que podem ser usadas no endpoint /ga4"
  });
});

// Endpoint para listar dimensões disponíveis
app.get('/ga4/dimensions', (req, res) => {
  const commonDimensions = [
    'country', 'city', 'region', 'continent',
    'deviceCategory', 'operatingSystem', 'browser',
    'source', 'medium', 'campaign', 'channelGroup',
    'pagePath', 'pageTitle', 'eventName',
    'date', 'hour', 'dayOfWeek', 'month'
  ];

  res.json({
    dimensions: commonDimensions,
    description: "Lista de dimensões comuns do GA4 que podem ser usadas no endpoint /ga4"
  });
});

// Endpoint de teste para verificar se a API está funcionando
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    message: 'Servidor GA4 API está funcionando corretamente'
  });
});

// Endpoint para obter dados formatados especificamente para LLM
app.post('/ga4/llm', async (req, res) => {
  try {
    const {
      propertyId = 'properties/SEU_PROPERTY_ID',
      startDate = '2024-06-01',
      endDate = '2024-06-28',
      query = 'Forneça um resumo geral dos dados'
    } = req.body;

    // Consulta básica com métricas principais
    const [report] = await client.runReport({
      property: propertyId,
      dateRanges: [{ startDate, endDate }],
      metrics: [
        { name: 'sessions' },
        { name: 'users' },
        { name: 'pageviews' },
        { name: 'bounceRate' }
      ],
      dimensions: [
        { name: 'country' },
        { name: 'deviceCategory' }
      ],
      limit: 10
    });

    // Formata especificamente para consumo por LLM
    const llmData = {
      query: query,
      period: `${startDate} a ${endDate}`,
      summary: generateDetailedLLMSummary(report, startDate, endDate),
      rawData: report.rows?.slice(0, 5) || [], // Limita para não sobrecarregar o LLM
      suggestions: [
        "Analise as tendências de tráfego por país",
        "Compare o desempenho entre dispositivos móveis e desktop",
        "Identifique padrões na taxa de rejeição",
        "Sugira otimizações baseadas nos dados"
      ]
    };

    res.json(llmData);
  } catch (err) {
    console.error('Erro ao consultar GA4 para LLM:', err);
    res.status(500).json({
      error: 'Erro ao consultar GA4 para LLM',
      message: err.message
    });
  }
});

function generateDetailedLLMSummary(report, startDate, endDate) {
  const totalRows = report.rowCount || 0;

  if (totalRows === 0) {
    return `Nenhum dado encontrado para o período de ${startDate} a ${endDate}.`;
  }

  const firstRow = report.rows?.[0];
  if (!firstRow) return "Dados não disponíveis.";

  const sessions = firstRow.metricValues[0]?.value || '0';
  const users = firstRow.metricValues[1]?.value || '0';
  const pageviews = firstRow.metricValues[2]?.value || '0';
  const bounceRate = firstRow.metricValues[3]?.value || '0';

  const country = firstRow.dimensionValues[0]?.value || 'N/A';
  const device = firstRow.dimensionValues[1]?.value || 'N/A';

  return `Período analisado: ${startDate} a ${endDate}
Total de registros: ${totalRows}

Métricas principais:
- Sessões: ${sessions}
- Usuários: ${users}
- Visualizações de página: ${pageviews}
- Taxa de rejeição: ${bounceRate}%

Principal país de origem: ${country}
Principal categoria de dispositivo: ${device}

Este resumo pode ser usado para análises mais detalhadas e geração de insights.`;
}

app.listen(port, () => {
  console.log(`🚀 Servidor GA4 API rodando em http://localhost:${port}`);
  console.log(`📊 Endpoints disponíveis:`);
  console.log(`   POST /ga4 - Consultar dados do GA4`);
  console.log(`   POST /ga4/llm - Dados formatados para LLM`);
  console.log(`   GET /ga4/metrics - Listar métricas disponíveis`);
  console.log(`   GET /ga4/dimensions - Listar dimensões disponíveis`);
  console.log(`   GET /health - Status do servidor`);
});
