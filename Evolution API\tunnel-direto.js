// Script que usa o ngrok instalado no sistema
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Caminhos possíveis do ngrok
const ngrokPaths = [
  'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Ngrok.Ngrok_Microsoft.Winget.Source_8wekyb3d8bbwe\\ngrok.exe',
  'ngrok.exe',
  'ngrok'
];

function findNgrok() {
  for (const ngrokPath of ngrokPaths) {
    try {
      if (fs.existsSync(ngrokPath)) {
        return ngrokPath;
      }
    } catch (error) {
      // Continuar tentando
    }
  }
  return null;
}

async function configurarToken() {
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise((resolve) => {
    console.log('🔑 CONFIGURAÇÃO DO NGROK');
    console.log('');
    console.log('📋 Se ainda não configurou:');
    console.log('1. Acesse: https://dashboard.ngrok.com/get-started/your-authtoken');
    console.log('2. Copie seu authtoken');
    console.log('');
    
    rl.question('🔑 Cole seu authtoken (ou Enter se já configurou): ', (token) => {
      rl.close();
      resolve(token.trim());
    });
  });
}

async function criarTunnel() {
  try {
    console.log('🔍 Procurando ngrok no sistema...');
    
    const ngrokPath = findNgrok();
    
    if (!ngrokPath) {
      throw new Error('ngrok não encontrado no sistema');
    }
    
    console.log(`✅ ngrok encontrado: ${ngrokPath}`);
    console.log('');
    
    // Configurar token se necessário
    const token = await configurarToken();
    
    if (token) {
      console.log('⚙️ Configurando authtoken...');
      
      // Configurar token primeiro
      const configProcess = spawn(ngrokPath, ['authtoken', token], {
        stdio: 'pipe'
      });
      
      await new Promise((resolve, reject) => {
        configProcess.on('close', (code) => {
          if (code === 0) {
            console.log('✅ Token configurado com sucesso');
            resolve();
          } else {
            reject(new Error('Erro ao configurar token'));
          }
        });
        
        configProcess.on('error', (error) => {
          reject(error);
        });
      });
    }
    
    console.log('');
    console.log('🌐 Iniciando túnel na porta 3000...');
    console.log('');
    console.log('⚠️  IMPORTANTE: Você verá uma URL como:');
    console.log('   https://abc123.ngrok.io');
    console.log('');
    console.log('📋 Essa será a URL base dos seus endpoints:');
    console.log('   https://abc123.ngrok.io/health');
    console.log('   https://abc123.ngrok.io/ga4');
    console.log('   https://abc123.ngrok.io/ga4/llm');
    console.log('   https://abc123.ngrok.io/ga4/metrics');
    console.log('   https://abc123.ngrok.io/ga4/dimensions');
    console.log('');
    console.log('🔗 Interface web: http://localhost:4040');
    console.log('');
    console.log('🛑 Para parar, pressione Ctrl+C');
    console.log('');
    console.log('═'.repeat(60));
    console.log('');
    
    // Iniciar túnel
    const tunnelProcess = spawn(ngrokPath, ['http', '3000'], {
      stdio: 'inherit'
    });
    
    // Capturar Ctrl+C
    process.on('SIGINT', () => {
      console.log('');
      console.log('🛑 Parando túnel...');
      tunnelProcess.kill();
      process.exit(0);
    });
    
    tunnelProcess.on('error', (error) => {
      console.error('❌ Erro ao iniciar túnel:', error.message);
      process.exit(1);
    });
    
    tunnelProcess.on('close', (code) => {
      console.log('');
      console.log('🛑 Túnel finalizado');
      process.exit(code);
    });
    
  } catch (error) {
    console.error('');
    console.error('❌ Erro:', error.message);
    console.error('');
    
    if (error.message.includes('não encontrado')) {
      console.log('💡 Soluções:');
      console.log('1. Instale o ngrok: winget install ngrok');
      console.log('2. Ou baixe de: https://ngrok.com/download');
      console.log('');
    }
    
    process.exit(1);
  }
}

// Verificar se o servidor está rodando
function verificarServidor() {
  return new Promise((resolve) => {
    const net = require('net');
    const client = new net.Socket();
    
    client.setTimeout(1000);
    
    client.on('connect', () => {
      client.destroy();
      resolve(true);
    });
    
    client.on('error', () => {
      resolve(false);
    });
    
    client.on('timeout', () => {
      client.destroy();
      resolve(false);
    });
    
    client.connect(3000, 'localhost');
  });
}

async function main() {
  console.log('🚀 Iniciando túnel ngrok para GA4 API');
  console.log('');
  console.log('🔍 Verificando servidor na porta 3000...');
  
  const servidorRodando = await verificarServidor();
  
  if (!servidorRodando) {
    console.log('❌ Servidor não está rodando na porta 3000');
    console.log('');
    console.log('🚀 Inicie o servidor primeiro:');
    console.log('   npm start');
    console.log('');
    console.log('📋 Depois execute este script:');
    console.log('   node tunnel-direto.js');
    console.log('');
    process.exit(1);
  }
  
  console.log('✅ Servidor detectado na porta 3000');
  console.log('');
  
  await criarTunnel();
}

main();
