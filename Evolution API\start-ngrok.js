// Script Node.js para iniciar ngrok
const ngrok = require('@ngrok/ngrok');
const readline = require('readline');

async function getAuthToken() {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise((resolve) => {
    console.log('🔑 CONFIGURAÇÃO DO NGROK');
    console.log('');
    console.log('📋 PASSOS:');
    console.log('1. A página do ngrok foi aberta no seu navegador');
    console.log('2. Faça login (ou crie conta gratuita)');
    console.log('3. Copie o authtoken que aparece na página');
    console.log('4. Cole aqui abaixo');
    console.log('');

    rl.question('🔑 Cole seu authtoken aqui: ', (token) => {
      rl.close();
      resolve(token.trim());
    });
  });
}

async function startTunnel() {
  try {
    console.log('🚀 Iniciando túnel ngrok...');
    console.log('');

    // Obter token do usuário
    const authtoken = await getAuthToken();

    if (!authtoken) {
      throw new Error('Token não pode estar vazio');
    }

    console.log('⚙️ Configurando túnel...');

    // Iniciar túnel na porta 3000 com token
    const url = await ngrok.forward({
      addr: 3000,
      authtoken: authtoken
    });
    
    console.log('✅ Túnel ngrok criado com sucesso!');
    console.log('');
    console.log('🌐 URL PÚBLICA DO SEU ENDPOINT:');
    console.log(`   ${url.toString()}`);
    console.log('');
    console.log('📋 Seus endpoints públicos:');
    console.log(`   ${url.toString()}/health          ← Status do servidor`);
    console.log(`   ${url.toString()}/ga4             ← Consulta GA4`);
    console.log(`   ${url.toString()}/ga4/llm         ← Dados para LLM`);
    console.log(`   ${url.toString()}/ga4/metrics     ← Lista métricas`);
    console.log(`   ${url.toString()}/ga4/dimensions  ← Lista dimensões`);
    console.log('');
    console.log('🔗 Interface web do ngrok: http://localhost:4040');
    console.log('');
    console.log('🛑 Para parar, pressione Ctrl+C');
    console.log('');
    
    // Manter o processo rodando
    process.on('SIGINT', async () => {
      console.log('');
      console.log('🛑 Parando túnel ngrok...');
      await ngrok.disconnect();
      process.exit(0);
    });
    
    // Manter vivo
    setInterval(() => {
      // Apenas para manter o processo rodando
    }, 1000);
    
  } catch (error) {
    console.error('❌ Erro ao iniciar túnel ngrok:');
    console.error(error.message);
    
    if (error.message.includes('authtoken')) {
      console.log('');
      console.log('🔑 Configure seu authtoken primeiro:');
      console.log('1. Acesse: https://dashboard.ngrok.com/get-started/your-authtoken');
      console.log('2. Copie seu token');
      console.log('3. Execute: ngrok authtoken SEU_TOKEN');
      console.log('');
    }
    
    process.exit(1);
  }
}

// Verificar se o servidor está rodando na porta 3000
const net = require('net');
const server = net.createServer();

server.listen(3000, (err) => {
  if (err) {
    console.log('✅ Servidor já está rodando na porta 3000');
    server.close();
    startTunnel();
  } else {
    console.log('❌ Nenhum servidor encontrado na porta 3000');
    console.log('');
    console.log('🚀 Inicie o servidor primeiro com:');
    console.log('   npm start');
    console.log('');
    console.log('📋 Depois execute este script novamente:');
    console.log('   node start-ngrok.js');
    server.close();
    process.exit(1);
  }
});

server.on('error', (err) => {
  if (err.code === 'EADDRINUSE') {
    console.log('✅ Servidor já está rodando na porta 3000');
    startTunnel();
  } else {
    console.error('❌ Erro ao verificar porta 3000:', err.message);
    process.exit(1);
  }
});
