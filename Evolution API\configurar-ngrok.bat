@echo off
echo 🔑 Configurador do ngrok Authtoken
echo.
echo 📋 PASSO 1: Obter seu token
echo    1. Acesse: https://dashboard.ngrok.com/signup
echo    2. <PERSON><PERSON><PERSON> login (ou crie conta gratuita)
echo    3. Vá para: https://dashboard.ngrok.com/get-started/your-authtoken
echo    4. Copie o token que aparece
echo.
echo 💡 O token é algo como: 2abc123def456ghi789jkl
echo.

set /p token="🔑 Cole seu token aqui e pressione Enter: "

if "%token%"=="" (
    echo ❌ Token não pode estar vazio!
    pause
    exit /b 1
)

echo.
echo ⚙️ Configurando token...

C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Ngrok.Ngrok_Microsoft.Winget.Source_8wekyb3d8bbwe\ngrok.exe authtoken %token%

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Token configurado com sucesso!
    echo.
    echo 🚀 Agora você pode usar:
    echo    .\start-tunnel.bat
    echo.
    echo 🧪 Ou testar com:
    echo    npm run tunnel
    echo.
) else (
    echo.
    echo ❌ Erro ao configurar token!
    echo    Verifique se o token está correto.
    echo.
)

pause
