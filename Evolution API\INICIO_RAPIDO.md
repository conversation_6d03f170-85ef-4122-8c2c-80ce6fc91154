# 🚀 Início Rápido - GA4 API + ngrok

## ✅ Status da Instalação
- ✅ **ngrok instalado** via winget
- ✅ **Servidor GA4 API** configurado e testado
- ✅ **Scripts automatizados** criados
- ⚠️ **Necessário:** Configurar authtoken do ngrok

## 🔑 Configuração Obrigatória do ngrok

### 1. <PERSON>riar conta gratuita
👉 https://dashboard.ngrok.com/signup

### 2. Obter authtoken
👉 https://dashboard.ngrok.com/get-started/your-authtoken

### 3. Configurar authtoken
```bash
C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Ngrok.Ngrok_Microsoft.Winget.Source_8wekyb3d8bbwe\ngrok.exe authtoken SEU_TOKEN_AQUI
```

## 🚀 Como Usar

### Opção 1: Script Automático (<PERSON><PERSON>)
```bash
.\start-tunnel.bat
```

### Opção 2: PowerShell Avançado
```bash
.\start-tunnel.ps1
```

### Opção 3: NPM Scripts
```bash
npm run start:tunnel
```

## 📋 O que acontece quando você executa:

1. **Servidor inicia** na porta 3000
2. **ngrok cria túnel** público
3. **Você recebe URL** como: `https://abc123.ngrok.io`

## 🌐 Seus Endpoints Públicos

Substitua `abc123.ngrok.io` pela URL que o ngrok fornecer:

- **Health Check:** `https://abc123.ngrok.io/health`
- **Consulta GA4:** `https://abc123.ngrok.io/ga4`
- **LLM Endpoint:** `https://abc123.ngrok.io/ga4/llm`
- **Métricas:** `https://abc123.ngrok.io/ga4/metrics`
- **Dimensões:** `https://abc123.ngrok.io/ga4/dimensions`

## 🧪 Teste Rápido

Após iniciar o túnel, teste com:
```bash
curl https://SUA_URL_NGROK.ngrok.io/health
```

## 🤖 Exemplo para LLM

```javascript
// Use a URL pública do ngrok
const response = await fetch('https://abc123.ngrok.io/ga4/llm', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    propertyId: 'properties/123456789',
    startDate: '2024-06-01',
    endDate: '2024-06-28',
    query: 'Analise o desempenho do site'
  })
});

const data = await response.json();
console.log(data.summary);
```

## 🔗 Links Úteis

- **Interface ngrok:** http://localhost:4040
- **Servidor local:** http://localhost:3000
- **Dashboard ngrok:** https://dashboard.ngrok.com

## 🛑 Para Parar

Pressione `Ctrl+C` no terminal onde o ngrok está rodando.

## ❓ Problemas Comuns

### "authentication failed"
👉 Configure o authtoken do ngrok (passo obrigatório acima)

### "Port already in use"
👉 Pare outros processos na porta 3000 ou mude a porta no código

### "ngrok not found"
👉 Use o caminho completo nos scripts (já configurado)

---

**🎉 Pronto! Seu servidor GA4 está acessível globalmente para integração com LLMs!**
